{% extends "simple_base.html" %}

{% block title %}المراسلات الصادرة - {{ super() }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-paper-plane me-2"></i>
        المراسلات الصادرة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <a href="{{ url_for('new_correspondence') }}" class="btn btn-primary">
            <i class="fas fa-plus me-2"></i>
            إنشاء مراسلة صادرة
        </a>
    </div>
</div>

{% if correspondences %}
<div class="card">
    <div class="card-header">
        <h5 class="mb-0">
            <i class="fas fa-list me-2"></i>
            قائمة المراسلات الصادرة ({{ correspondences|length }})
        </h5>
    </div>
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-hover">
                <thead>
                    <tr>
                        <th>الرقم الداخلي</th>
                        <th>الموضوع</th>
                        <th>المستقبل</th>
                        <th>الجهة المستقبلة</th>
                        <th>الأولوية</th>
                        <th>الحالة</th>
                        <th>تاريخ الإنشاء</th>
                        <th>تاريخ الإرسال</th>
                        <th>الإجراءات</th>
                    </tr>
                </thead>
                <tbody>
                    {% for correspondence in correspondences %}
                    <tr>
                        <td>
                            <span class="badge bg-primary">{{ correspondence.internal_number }}</span>
                        </td>
                        <td>
                            <div class="text-truncate correspondence-subject-wide" title="{{ correspondence.subject }}">
                                <strong>{{ correspondence.subject }}</strong>
                            </div>
                            {% if correspondence.content %}
                            <small class="text-muted text-truncate d-block correspondence-content-preview">
                                {{ correspondence.content[:50] }}...
                            </small>
                            {% endif %}
                        </td>
                        <td>
                            {{ correspondence.recipient_name or 'غير محدد' }}
                        </td>
                        <td>
                            {{ correspondence.recipient_organization or 'غير محدد' }}
                        </td>
                        <td>
                            <span class="badge priority-{{ correspondence.priority }}">
                                {% if correspondence.priority == 'عادي' %}
                                    <i class="fas fa-circle"></i>
                                {% elif correspondence.priority == 'عاجل' %}
                                    <i class="fas fa-exclamation-circle"></i>
                                {% elif correspondence.priority == 'سري' %}
                                    <i class="fas fa-lock"></i>
                                {% endif %}
                                {{ correspondence.priority }}
                            </span>
                        </td>
                        <td>
                            <span class="badge status-{{ correspondence.status.replace(' ', '-') }}">
                                {{ correspondence.status }}
                            </span>
                        </td>
                        <td>
                            {{ correspondence.date_created.strftime('%Y-%m-%d') }}
                            <br>
                            <small class="text-muted">{{ correspondence.date_created.strftime('%H:%M') }}</small>
                        </td>
                        <td>
                            {% if correspondence.date_sent %}
                                {{ correspondence.date_sent.strftime('%Y-%m-%d') }}
                                <br>
                                <small class="text-muted">{{ correspondence.date_sent.strftime('%H:%M') }}</small>
                            {% else %}
                                <span class="text-warning">
                                    <i class="fas fa-clock me-1"></i>
                                    لم يتم الإرسال
                                </span>
                            {% endif %}
                        </td>
                        <td>
                            <div class="btn-group btn-group-sm" role="group">
                                <a href="{{ url_for('view_correspondence', id=correspondence.id) }}"
                                   class="btn btn-outline-primary" title="عرض التفاصيل">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <button type="button" class="btn btn-outline-success" title="تحديث الحالة">
                                    <i class="fas fa-edit"></i>
                                </button>
                                {% if not correspondence.date_sent %}
                                <button type="button" class="btn btn-outline-info" title="تحديد كمرسل"
                                        onclick="markAsSent({{ correspondence.id }})">
                                    <i class="fas fa-paper-plane"></i>
                                </button>
                                {% endif %}
                                {% if correspondence.is_archived %}
                                <button type="button" class="btn btn-outline-secondary" title="مؤرشف">
                                    <i class="fas fa-archive"></i>
                                </button>
                                {% else %}
                                <button type="button" class="btn btn-outline-warning" title="أرشفة">
                                    <i class="fas fa-archive"></i>
                                </button>
                                {% endif %}
                            </div>
                        </td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </div>
    </div>
</div>

<!-- Statistics Summary -->
<div class="row mt-4">
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-primary">{{ correspondences|selectattr("status", "equalto", "جديد")|list|length }}</h5>
                <p class="card-text">مسودات</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-success">{{ correspondences|selectattr("date_sent", "ne", none)|list|length }}</h5>
                <p class="card-text">مرسلة</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-warning">{{ correspondences|rejectattr("date_sent")|list|length }}</h5>
                <p class="card-text">في الانتظار</p>
            </div>
        </div>
    </div>
    <div class="col-md-3">
        <div class="card text-center">
            <div class="card-body">
                <h5 class="card-title text-secondary">{{ correspondences|selectattr("is_archived", "equalto", true)|list|length }}</h5>
                <p class="card-text">مؤرشفة</p>
            </div>
        </div>
    </div>
</div>

{% else %}
<div class="card">
    <div class="card-body text-center py-5">
        <i class="fas fa-paper-plane fa-5x text-muted mb-4"></i>
        <h3 class="text-muted mb-3">لا توجد مراسلات صادرة</h3>
        <p class="text-muted mb-4">لم يتم إنشاء أي مراسلات صادرة بعد</p>
        <a href="{{ url_for('new_correspondence') }}" class="btn btn-primary btn-lg">
            <i class="fas fa-plus me-2"></i>
            إنشاء أول مراسلة صادرة
        </a>
    </div>
</div>
{% endif %}

<script>
function markAsSent(correspondenceId) {
    if (confirm('هل تريد تحديد هذه المراسلة كمرسلة؟')) {
        // هنا يمكن إضافة طلب AJAX لتحديث حالة الإرسال
        alert('تم تحديد المراسلة كمرسلة بنجاح');
        location.reload();
    }
}

function archiveCorrespondence(correspondenceId) {
    if (confirm('هل أنت متأكد من أرشفة هذه المراسلة؟')) {
        // هنا يمكن إضافة طلب AJAX للأرشفة
        alert('تم أرشفة المراسلة بنجاح');
        location.reload();
    }
}
</script>

{% endblock %}
