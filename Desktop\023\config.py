# -*- coding: utf-8 -*-
"""
إعدادات نظام المراسلات الإلكترونية
Electronic Correspondence System Configuration
"""

import os
from datetime import timedelta

class Config:
    """إعدادات النظام الأساسية"""
    
    # إعدادات التطبيق الأساسية
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-here-change-in-production'
    
    # إعدادات قاعدة البيانات
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL') or 'sqlite:///correspondence.db'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # إعدادات الجلسات
    PERMANENT_SESSION_LIFETIME = timedelta(hours=8)
    SESSION_COOKIE_SECURE = False  # True في الإنتاج مع HTTPS
    SESSION_COOKIE_HTTPONLY = True
    
    # إعدادات الملفات
    UPLOAD_FOLDER = 'static/uploads'
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB حد أقصى لحجم الملف
    ALLOWED_EXTENSIONS = {'txt', 'pdf', 'png', 'jpg', 'jpeg', 'gif', 'doc', 'docx', 'xls', 'xlsx'}
    
    # إعدادات النظام
    SYSTEM_NAME = "نظام المراسلات الإلكترونية"
    SYSTEM_VERSION = "1.0.0"
    ORGANIZATION_NAME = "الجهة الحكومية"
    
    # إعدادات الأمان
    WTF_CSRF_ENABLED = True
    WTF_CSRF_TIME_LIMIT = 3600
    
    # إعدادات التقارير
    REPORTS_PER_PAGE = 50
    CORRESPONDENCE_PER_PAGE = 20
    
    # إعدادات الشبكة
    HOST = '0.0.0.0'  # للوصول من أي جهاز في الشبكة
    PORT = 5000
    DEBUG = True  # False في الإنتاج

class DevelopmentConfig(Config):
    """إعدادات التطوير"""
    DEBUG = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///correspondence_dev.db'

class ProductionConfig(Config):
    """إعدادات الإنتاج"""
    DEBUG = False
    SECRET_KEY = os.environ.get('SECRET_KEY')
    SQLALCHEMY_DATABASE_URI = os.environ.get('DATABASE_URL')

class TestingConfig(Config):
    """إعدادات الاختبار"""
    TESTING = True
    SQLALCHEMY_DATABASE_URI = 'sqlite:///correspondence_test.db'
    WTF_CSRF_ENABLED = False

# اختيار الإعدادات حسب البيئة
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}
