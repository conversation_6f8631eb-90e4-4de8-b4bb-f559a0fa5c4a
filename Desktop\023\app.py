# -*- coding: utf-8 -*-
"""
التطبيق الرئيسي لنظام المراسلات الإلكترونية
Main Application for Electronic Correspondence System
"""

import os
import socket
from flask import Flask, render_template, redirect, url_for, flash, request
from flask_login import LoginManager, login_required, current_user
from flask_sqlalchemy import SQLAlchemy
from config import config

# إنشاء قاعدة البيانات
db = SQLAlchemy()

# استيراد النماذج بعد تعريف db
from models.user import User, Role, Department
from models.correspondence import Correspondence, CorrespondenceType, Attachment, CorrespondenceAction

def create_app(config_name=None):
    """إنشاء تطبيق Flask"""
    if config_name is None:
        config_name = os.environ.get('FLASK_CONFIG', 'default')

    app = Flask(__name__)
    app.config.from_object(config[config_name])

    # تهيئة الإضافات
    db.init_app(app)

    # إعداد نظام تسجيل الدخول
    login_manager = LoginManager()
    login_manager.init_app(app)
    login_manager.login_view = 'auth.login'
    login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'
    login_manager.login_message_category = 'info'

    @login_manager.user_loader
    def load_user(user_id):
        return User.query.get(int(user_id))

    # تسجيل المسارات (Blueprints) - معطل مؤقتاً
    # from routes.auth import auth_bp
    # from routes.dashboard import dashboard_bp
    # from routes.inbox import inbox_bp
    # from routes.outbox import outbox_bp
    # from routes.archive import archive_bp
    # from routes.reports import reports_bp
    # from routes.admin import admin_bp

    # app.register_blueprint(auth_bp, url_prefix='/auth')
    # app.register_blueprint(dashboard_bp, url_prefix='/')
    # app.register_blueprint(inbox_bp, url_prefix='/inbox')
    # app.register_blueprint(outbox_bp, url_prefix='/outbox')
    # app.register_blueprint(archive_bp, url_prefix='/archive')
    # app.register_blueprint(reports_bp, url_prefix='/reports')
    # app.register_blueprint(admin_bp, url_prefix='/admin')

    # إنشاء قاعدة البيانات والبيانات الأولية
    with app.app_context():
        db.create_all()
        create_initial_data()

    # إنشاء مجلد الرفع إذا لم يكن موجوداً
    upload_folder = os.path.join(app.root_path, app.config['UPLOAD_FOLDER'])
    if not os.path.exists(upload_folder):
        os.makedirs(upload_folder)

    # الصفحة الرئيسية
    @app.route('/')
    def index():
        if current_user.is_authenticated:
            return redirect(url_for('dashboard.dashboard'))
        return redirect(url_for('auth.login'))

    # معالج الأخطاء
    @app.errorhandler(404)
    def not_found_error(error):
        return render_template('errors/404.html'), 404

    @app.errorhandler(500)
    def internal_error(error):
        db.session.rollback()
        return render_template('errors/500.html'), 500

    # متغيرات القوالب العامة
    @app.context_processor
    def inject_globals():
        return {
            'system_name': app.config['SYSTEM_NAME'],
            'organization_name': app.config['ORGANIZATION_NAME'],
            'system_version': app.config['SYSTEM_VERSION'],
            'current_ip': get_local_ip()
        }

    return app

def create_initial_data():
    """إنشاء البيانات الأولية"""

    # إنشاء الأدوار الأساسية
    if not Role.query.first():
        roles = [
            {
                'name': 'admin',
                'name_ar': 'مدير النظام',
                'description': 'مدير النظام - صلاحيات كاملة',
                'can_read_all': True,
                'can_write': True,
                'can_delete': True,
                'can_manage_users': True,
                'can_view_reports': True,
                'can_archive': True,
                'can_approve': True
            },
            {
                'name': 'manager',
                'name_ar': 'مدير إدارة',
                'description': 'مدير إدارة - صلاحيات إدارية',
                'can_read_all': True,
                'can_write': True,
                'can_delete': False,
                'can_manage_users': False,
                'can_view_reports': True,
                'can_archive': True,
                'can_approve': True
            },
            {
                'name': 'employee',
                'name_ar': 'موظف',
                'description': 'موظف عادي',
                'can_read_all': False,
                'can_write': True,
                'can_delete': False,
                'can_manage_users': False,
                'can_view_reports': False,
                'can_archive': False,
                'can_approve': False
            },
            {
                'name': 'secretary',
                'name_ar': 'سكرتير',
                'description': 'سكرتير - إدخال وتنظيم المراسلات',
                'can_read_all': True,
                'can_write': True,
                'can_delete': False,
                'can_manage_users': False,
                'can_view_reports': False,
                'can_archive': True,
                'can_approve': False
            }
        ]

        for role_data in roles:
            role = Role(**role_data)
            db.session.add(role)

    # إنشاء الإدارات الأساسية
    if not Department.query.first():
        departments = [
            {
                'name': 'Administration',
                'name_ar': 'الإدارة العامة',
                'code': 'ADM',
                'description': 'الإدارة العامة للمؤسسة'
            },
            {
                'name': 'IT',
                'name_ar': 'تقنية المعلومات',
                'code': 'IT',
                'description': 'إدارة تقنية المعلومات'
            },
            {
                'name': 'HR',
                'name_ar': 'الموارد البشرية',
                'code': 'HR',
                'description': 'إدارة الموارد البشرية'
            },
            {
                'name': 'Finance',
                'name_ar': 'الشؤون المالية',
                'code': 'FIN',
                'description': 'إدارة الشؤون المالية'
            }
        ]

        for dept_data in departments:
            dept = Department(**dept_data)
            db.session.add(dept)

    # إنشاء المستخدم الافتراضي
    if not User.query.first():
        admin_role = Role.query.filter_by(name='admin').first()
        admin_dept = Department.query.filter_by(code='ADM').first()

        admin_user = User(
            username='admin',
            email='<EMAIL>',
            first_name='مدير',
            last_name='النظام',
            full_name_ar='مدير النظام',
            employee_id='ADM001',
            phone='123456789',
            role_id=admin_role.id,
            department_id=admin_dept.id,
            is_active=True,
            is_verified=True
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)

    db.session.commit()

def get_local_ip():
    """الحصول على عنوان IP المحلي"""
    try:
        # الاتصال بخادم خارجي للحصول على IP المحلي
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return '127.0.0.1'

if __name__ == '__main__':
    app = create_app()

    # عرض معلومات الشبكة
    local_ip = get_local_ip()
    port = app.config['PORT']

    print("=" * 60)
    print(f"🚀 {app.config['SYSTEM_NAME']}")
    print(f"📍 النسخة: {app.config['SYSTEM_VERSION']}")
    print("=" * 60)
    print(f"🌐 عناوين الوصول:")
    print(f"   المحلي: http://127.0.0.1:{port}")
    print(f"   الشبكة: http://{local_ip}:{port}")
    print("=" * 60)
    print(f"👤 بيانات الدخول الافتراضية:")
    print(f"   اسم المستخدم: admin")
    print(f"   كلمة المرور: admin123")
    print("=" * 60)

    app.run(
        host=app.config['HOST'],
        port=port,
        debug=app.config['DEBUG']
    )
