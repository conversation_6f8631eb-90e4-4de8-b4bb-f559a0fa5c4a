{% extends "simple_base.html" %}

{% block title %}تفاصيل المراسلة - {{ super() }}{% endblock %}

{% block content %}
<div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pt-3 pb-2 mb-3 border-bottom">
    <h1 class="h2">
        <i class="fas fa-eye me-2"></i>
        تفاصيل المراسلة
    </h1>
    <div class="btn-toolbar mb-2 mb-md-0">
        <div class="btn-group me-2">
            {% if correspondence.direction == 'وارد' %}
            <a href="{{ url_for('inbox') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للواردة
            </a>
            {% else %}
            <a href="{{ url_for('outbox') }}" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-right me-2"></i>
                العودة للصادرة
            </a>
            {% endif %}
            <button onclick="window.print()" class="btn btn-outline-primary">
                <i class="fas fa-print me-2"></i>
                طباعة
            </button>
        </div>
    </div>
</div>

<div class="row">
    <!-- معلومات المراسلة الأساسية -->
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-info-circle me-2"></i>
                    معلومات المراسلة
                </h5>
            </div>
            <div class="card-body">
                <!-- رأس المراسلة -->
                <div class="row mb-4">
                    <div class="col-md-6">
                        <h6 class="text-muted">الرقم الداخلي</h6>
                        <h4 class="text-primary">{{ correspondence.internal_number }}</h4>
                    </div>
                    <div class="col-md-6">
                        {% if correspondence.external_number %}
                        <h6 class="text-muted">رقم صادر الجهة</h6>
                        <h4 class="text-secondary">{{ correspondence.external_number }}</h4>
                        {% endif %}
                    </div>
                </div>

                <!-- الموضوع -->
                <div class="mb-4">
                    <h6 class="text-muted">الموضوع</h6>
                    <h3 class="text-dark">{{ correspondence.subject }}</h3>
                </div>

                <!-- المحتوى -->
                {% if correspondence.content %}
                <div class="mb-4">
                    <h6 class="text-muted">المحتوى</h6>
                    <div class="border rounded p-3 bg-light">
                        <p class="mb-0 correspondence-content-formatted">{{ correspondence.content }}</p>
                    </div>
                </div>
                {% endif %}

                <!-- معلومات الإرسال/الاستقبال -->
                <div class="row mb-4">
                    {% if correspondence.direction == 'وارد' %}
                    <div class="col-md-6">
                        <h6 class="text-muted">المرسل</h6>
                        <p class="mb-1"><strong>{{ correspondence.sender_name or 'غير محدد' }}</strong></p>
                        <p class="text-muted">{{ correspondence.sender_organization or 'غير محدد' }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">تاريخ الاستلام</h6>
                        <p class="mb-0">
                            {% if correspondence.date_received %}
                                {{ correspondence.date_received.strftime('%Y-%m-%d') }}
                                <br>
                                <small class="text-muted">{{ correspondence.date_received.strftime('%H:%M') }}</small>
                            {% else %}
                                <span class="text-muted">غير محدد</span>
                            {% endif %}
                        </p>
                    </div>
                    {% else %}
                    <div class="col-md-6">
                        <h6 class="text-muted">المستقبل</h6>
                        <p class="mb-1"><strong>{{ correspondence.recipient_name or 'غير محدد' }}</strong></p>
                        <p class="text-muted">{{ correspondence.recipient_organization or 'غير محدد' }}</p>
                    </div>
                    <div class="col-md-6">
                        <h6 class="text-muted">تاريخ الإرسال</h6>
                        <p class="mb-0">
                            {% if correspondence.date_sent %}
                                {{ correspondence.date_sent.strftime('%Y-%m-%d') }}
                                <br>
                                <small class="text-muted">{{ correspondence.date_sent.strftime('%H:%M') }}</small>
                            {% else %}
                                <span class="text-muted">لم يتم الإرسال بعد</span>
                            {% endif %}
                        </p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>

    <!-- معلومات إضافية -->
    <div class="col-lg-4">
        <!-- حالة المراسلة -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-chart-line me-2"></i>
                    حالة المراسلة
                </h5>
            </div>
            <div class="card-body text-center">
                <div class="mb-3">
                    <span class="badge bg-info fs-6 p-2">{{ correspondence.direction }}</span>
                </div>
                <div class="mb-3">
                    <span class="badge status-{{ correspondence.status.replace(' ', '-') }} fs-6 p-2">
                        {{ correspondence.status }}
                    </span>
                </div>
                <div class="mb-3">
                    <span class="badge priority-{{ correspondence.priority }} fs-6 p-2">
                        {% if correspondence.priority == 'عادي' %}
                            <i class="fas fa-circle me-1"></i>
                        {% elif correspondence.priority == 'عاجل' %}
                            <i class="fas fa-exclamation-circle me-1"></i>
                        {% elif correspondence.priority == 'سري' %}
                            <i class="fas fa-lock me-1"></i>
                        {% endif %}
                        {{ correspondence.priority }}
                    </span>
                </div>
                {% if correspondence.is_archived %}
                <div class="mb-3">
                    <span class="badge bg-secondary fs-6 p-2">
                        <i class="fas fa-archive me-1"></i>
                        مؤرشف
                    </span>
                </div>
                {% endif %}
            </div>
        </div>

        <!-- معلومات النظام -->
        <div class="card mb-3">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-cog me-2"></i>
                    معلومات النظام
                </h5>
            </div>
            <div class="card-body">
                <div class="mb-2">
                    <small class="text-muted">تاريخ الإنشاء:</small>
                    <br>
                    <strong>{{ correspondence.date_created.strftime('%Y-%m-%d %H:%M') }}</strong>
                </div>
                <div class="mb-2">
                    <small class="text-muted">أنشأ بواسطة:</small>
                    <br>
                    <strong>{{ correspondence.created_by.full_name_ar }}</strong>
                </div>
                <div class="mb-2">
                    <small class="text-muted">رقم المراسلة:</small>
                    <br>
                    <code>{{ correspondence.id }}</code>
                </div>
            </div>
        </div>

        <!-- إجراءات سريعة -->
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0">
                    <i class="fas fa-bolt me-2"></i>
                    إجراءات سريعة
                </h5>
            </div>
            <div class="card-body">
                <div class="d-grid gap-2">
                    <button class="btn btn-outline-primary" onclick="updateStatus()">
                        <i class="fas fa-edit me-2"></i>
                        تحديث الحالة
                    </button>
                    {% if not correspondence.is_archived %}
                    <button class="btn btn-outline-warning" onclick="archiveCorrespondence()">
                        <i class="fas fa-archive me-2"></i>
                        أرشفة
                    </button>
                    {% else %}
                    <button class="btn btn-outline-info" onclick="unarchiveCorrespondence()">
                        <i class="fas fa-undo me-2"></i>
                        إلغاء الأرشفة
                    </button>
                    {% endif %}
                    <button class="btn btn-outline-success" onclick="copyToClipboard()">
                        <i class="fas fa-copy me-2"></i>
                        نسخ الرابط
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>

<script>
function updateStatus() {
    // هنا يمكن إضافة نافذة منبثقة لتحديث الحالة
    alert('ميزة تحديث الحالة قيد التطوير');
}

function archiveCorrespondence() {
    if (confirm('هل أنت متأكد من أرشفة هذه المراسلة؟')) {
        // هنا يمكن إضافة طلب AJAX للأرشفة
        alert('تم أرشفة المراسلة بنجاح');
        location.reload();
    }
}

function unarchiveCorrespondence() {
    if (confirm('هل أنت متأكد من إلغاء أرشفة هذه المراسلة؟')) {
        // هنا يمكن إضافة طلب AJAX لإلغاء الأرشفة
        alert('تم إلغاء أرشفة المراسلة بنجاح');
        location.reload();
    }
}

function copyToClipboard() {
    const url = window.location.href;
    navigator.clipboard.writeText(url).then(function() {
        alert('تم نسخ رابط المراسلة إلى الحافظة');
    }, function() {
        alert('فشل في نسخ الرابط');
    });
}

// إخفاء عناصر معينة عند الطباعة
window.addEventListener('beforeprint', function() {
    document.querySelector('.btn-toolbar').style.display = 'none';
    document.querySelector('.card:last-child').style.display = 'none';
});

window.addEventListener('afterprint', function() {
    document.querySelector('.btn-toolbar').style.display = 'block';
    document.querySelector('.card:last-child').style.display = 'block';
});
</script>

{% endblock %}
