<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - {{ system_name }}</title>

    <!-- Bootstrap CSS RTL -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.rtl.min.css" rel="stylesheet">
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <!-- Google Fonts - Arabic -->
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">

    <style>
        body {
            font-family: 'Cairo', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .login-container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0,0,0,0.1);
            overflow: hidden;
            max-width: 900px;
            width: 100%;
        }

        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 40px;
            text-align: center;
        }

        .login-header h1 {
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .login-header p {
            font-size: 1.1rem;
            opacity: 0.9;
            margin-bottom: 0;
        }

        .login-form {
            padding: 40px;
        }

        .form-control {
            border: 2px solid #e9ecef;
            border-radius: 10px;
            padding: 12px 15px;
            font-size: 1rem;
            transition: all 0.3s ease;
        }

        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }

        .input-group-text {
            background-color: #f8f9fa;
            border: 2px solid #e9ecef;
            border-left: none;
            border-radius: 0 10px 10px 0;
        }

        .input-group .form-control {
            border-left: none;
            border-radius: 10px 0 0 10px;
        }

        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-size: 1.1rem;
            font-weight: 600;
            color: white;
            width: 100%;
            transition: all 0.3s ease;
        }

        .btn-login:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .system-info {
            background-color: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
            text-align: center;
        }

        .system-info h5 {
            color: #495057;
            margin-bottom: 15px;
        }

        .system-info .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 8px 0;
            border-bottom: 1px solid #dee2e6;
        }

        .system-info .info-item:last-child {
            border-bottom: none;
        }

        .system-info .info-label {
            font-weight: 600;
            color: #6c757d;
        }

        .system-info .info-value {
            color: #495057;
            font-family: 'Courier New', monospace;
        }

        .alert {
            border: none;
            border-radius: 10px;
            font-weight: 500;
        }

        .demo-credentials {
            background-color: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 10px;
            padding: 15px;
            margin-top: 20px;
        }

        .demo-credentials h6 {
            color: #0066cc;
            margin-bottom: 10px;
        }

        .demo-credentials .credential-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 5px;
        }

        @media (max-width: 768px) {
            .login-container {
                margin: 20px;
                border-radius: 15px;
            }

            .login-header {
                padding: 30px 20px;
            }

            .login-header h1 {
                font-size: 2rem;
            }

            .login-form {
                padding: 30px 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="row g-0">
                <div class="col-lg-6">
                    <div class="login-header">
                        <i class="fas fa-envelope-open-text fa-3x mb-3"></i>
                        <h1>{{ system_name }}</h1>
                        <p>نظام إدارة المراسلات الإلكترونية</p>
                        <p class="mt-3">{{ organization_name }}</p>
                    </div>
                </div>

                <div class="col-lg-6">
                    <div class="login-form">
                        <h3 class="text-center mb-4">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            تسجيل الدخول
                        </h3>

                        <!-- Flash Messages -->
                        {% with messages = get_flashed_messages(with_categories=true) %}
                            {% if messages %}
                                {% for category, message in messages %}
                                    <div class="alert alert-{{ 'danger' if category == 'error' else category }} alert-dismissible fade show" role="alert">
                                        <i class="fas fa-{{ 'exclamation-triangle' if category == 'error' else 'info-circle' if category == 'info' else 'check-circle' }} me-2"></i>
                                        {{ message }}
                                        <button type="button" class="btn-close" data-bs-dismiss="alert"
                                                aria-label="إغلاق التنبيه" title="إغلاق التنبيه"></button>
                                    </div>
                                {% endfor %}
                            {% endif %}
                        {% endwith %}

                        <form method="POST">
                            <div class="mb-3">
                                <label for="username" class="form-label">اسم المستخدم أو البريد الإلكتروني</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" id="username" name="username" required>
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">كلمة المرور</label>
                                <div class="input-group">
                                    <input type="password" class="form-control" id="password" name="password" required>
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                </div>
                            </div>

                            <div class="mb-3 form-check">
                                <input type="checkbox" class="form-check-input" id="remember_me" name="remember_me">
                                <label class="form-check-label" for="remember_me">
                                    تذكرني
                                </label>
                            </div>

                            <button type="submit" class="btn btn-login">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                دخول
                            </button>
                        </form>

                        <!-- Demo Credentials -->
                        <div class="demo-credentials">
                            <h6><i class="fas fa-info-circle me-2"></i>بيانات الدخول التجريبية</h6>
                            <div class="credential-item">
                                <span>اسم المستخدم:</span>
                                <code>admin</code>
                            </div>
                            <div class="credential-item">
                                <span>كلمة المرور:</span>
                                <code>admin123</code>
                            </div>
                        </div>

                        <!-- System Info -->
                        <div class="system-info">
                            <h5><i class="fas fa-info-circle me-2"></i>معلومات النظام</h5>
                            <div class="info-item">
                                <span class="info-label">عنوان الشبكة:</span>
                                <span class="info-value">{{ current_ip }}:5000</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">النسخة:</span>
                                <span class="info-value">{{ system_version }}</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">التاريخ:</span>
                                <span class="info-value" id="current-date"></span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <script>
        // Display current date
        document.getElementById('current-date').textContent = new Date().toLocaleDateString('ar-SA');

        // Auto-hide alerts after 5 seconds
        setTimeout(function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                alert.style.opacity = '0';
                setTimeout(() => alert.remove(), 300);
            });
        }, 5000);

        // Focus on username field
        document.getElementById('username').focus();
    </script>
</body>
</html>
