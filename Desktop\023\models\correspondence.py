# -*- coding: utf-8 -*-
"""
نموذج المراسلات والمرفقات والإجراءات
Correspondence, Attachments, and Actions Models
"""

from datetime import datetime
from . import db

class CorrespondenceType(db.Model):
    """أنواع المراسلات"""
    __tablename__ = 'correspondence_types'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    name_ar = db.Column(db.String(64), nullable=False)
    code = db.Column(db.String(10), unique=True, nullable=False)
    description = db.Column(db.Text)
    color = db.Column(db.String(7), default='#007bff')  # لون للتمييز
    
    is_active = db.Column(db.Bo<PERSON>, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    correspondences = db.relationship('Correspondence', backref='type', lazy='dynamic')
    
    def __repr__(self):
        return f'<CorrespondenceType {self.name_ar}>'

class Correspondence(db.Model):
    """نموذج المراسلات"""
    __tablename__ = 'correspondences'
    
    id = db.Column(db.Integer, primary_key=True)
    
    # أرقام المراسلة
    internal_number = db.Column(db.String(20), unique=True, nullable=False)  # الرقم الداخلي
    external_number = db.Column(db.String(50))  # رقم صادر الجهة
    
    # معلومات أساسية
    subject = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text)
    
    # التواريخ
    date_received = db.Column(db.DateTime)  # تاريخ الاستلام
    date_created = db.Column(db.DateTime, default=datetime.utcnow)
    date_sent = db.Column(db.DateTime)  # تاريخ الإرسال
    due_date = db.Column(db.DateTime)  # تاريخ الاستحقاق
    
    # الجهات
    sender_name = db.Column(db.String(255))  # اسم المرسل
    sender_organization = db.Column(db.String(255))  # الجهة المرسلة
    recipient_name = db.Column(db.String(255))  # اسم المستقبل
    recipient_organization = db.Column(db.String(255))  # الجهة المستقبلة
    
    # التصنيف والأولوية
    priority = db.Column(db.String(20), default='عادي')  # عادي، عاجل، سري
    classification = db.Column(db.String(50))  # التصنيف
    
    # الحالة والاتجاه
    direction = db.Column(db.String(10), nullable=False)  # وارد، صادر
    status = db.Column(db.String(20), default='جديد')  # جديد، قيد المراجعة، مكتمل، مؤرشف
    
    # الإجراءات
    action_required = db.Column(db.String(255))  # الإجراء المطلوب
    action_taken = db.Column(db.Text)  # الإجراء المتخذ
    notes = db.Column(db.Text)  # ملاحظات
    
    # معلومات النظام
    is_archived = db.Column(db.Boolean, default=False)
    is_confidential = db.Column(db.Boolean, default=False)
    
    # المفاتيح الخارجية
    type_id = db.Column(db.Integer, db.ForeignKey('correspondence_types.id'))
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    assigned_to_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'))
    
    # العلاقات
    created_by = db.relationship('User', foreign_keys=[created_by_id], backref='created_correspondences')
    assigned_to = db.relationship('User', foreign_keys=[assigned_to_id], backref='assigned_correspondences')
    attachments = db.relationship('Attachment', backref='correspondence', lazy='dynamic', cascade='all, delete-orphan')
    actions = db.relationship('CorrespondenceAction', backref='correspondence', lazy='dynamic', cascade='all, delete-orphan')
    
    def generate_internal_number(self):
        """توليد رقم داخلي تلقائي"""
        year = datetime.now().year
        count = Correspondence.query.filter(
            db.extract('year', Correspondence.date_created) == year
        ).count() + 1
        
        if self.direction == 'صادر':
            prefix = 'OUT'
        else:
            prefix = 'IN'
            
        self.internal_number = f"{prefix}-{year}-{count:04d}"
    
    @property
    def days_since_received(self):
        """عدد الأيام منذ الاستلام"""
        if self.date_received:
            return (datetime.utcnow() - self.date_received).days
        return 0
    
    @property
    def is_overdue(self):
        """هل المراسلة متأخرة؟"""
        if self.due_date:
            return datetime.utcnow() > self.due_date
        return False
    
    def __repr__(self):
        return f'<Correspondence {self.internal_number}>'

class Attachment(db.Model):
    """نموذج المرفقات"""
    __tablename__ = 'attachments'
    
    id = db.Column(db.Integer, primary_key=True)
    filename = db.Column(db.String(255), nullable=False)
    original_filename = db.Column(db.String(255), nullable=False)
    file_path = db.Column(db.String(500), nullable=False)
    file_size = db.Column(db.Integer)  # بالبايت
    file_type = db.Column(db.String(50))
    mime_type = db.Column(db.String(100))
    
    description = db.Column(db.Text)
    
    uploaded_at = db.Column(db.DateTime, default=datetime.utcnow)
    uploaded_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    correspondence_id = db.Column(db.Integer, db.ForeignKey('correspondences.id'), nullable=False)
    
    # العلاقات
    uploaded_by = db.relationship('User', backref='uploaded_attachments')
    
    @property
    def file_size_mb(self):
        """حجم الملف بالميجابايت"""
        if self.file_size:
            return round(self.file_size / (1024 * 1024), 2)
        return 0
    
    def __repr__(self):
        return f'<Attachment {self.original_filename}>'

class CorrespondenceAction(db.Model):
    """نموذج إجراءات المراسلات"""
    __tablename__ = 'correspondence_actions'
    
    id = db.Column(db.Integer, primary_key=True)
    action_type = db.Column(db.String(50), nullable=False)  # إنشاء، تحديث، تحويل، أرشفة
    description = db.Column(db.Text, nullable=False)
    notes = db.Column(db.Text)
    
    # معلومات التحويل
    transferred_from_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    transferred_to_id = db.Column(db.Integer, db.ForeignKey('users.id'))
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    created_by_id = db.Column(db.Integer, db.ForeignKey('users.id'), nullable=False)
    correspondence_id = db.Column(db.Integer, db.ForeignKey('correspondences.id'), nullable=False)
    
    # العلاقات
    created_by = db.relationship('User', foreign_keys=[created_by_id], backref='created_actions')
    transferred_from = db.relationship('User', foreign_keys=[transferred_from_id])
    transferred_to = db.relationship('User', foreign_keys=[transferred_to_id])
    
    def __repr__(self):
        return f'<CorrespondenceAction {self.action_type}>'
