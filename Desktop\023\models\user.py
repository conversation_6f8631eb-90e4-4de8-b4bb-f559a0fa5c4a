# -*- coding: utf-8 -*-
"""
نموذج المستخدمين والأدوار والإدارات
User, Role, and Department Models
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash
from . import db

class Role(db.Model):
    """نموذج الأدوار والصلاحيات"""
    __tablename__ = 'roles'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False)
    name_ar = db.Column(db.String(64), nullable=False)  # الاسم بالعربية
    description = db.Column(db.Text)
    
    # الصلاحيات
    can_read_all = db.Column(db.<PERSON>, default=False)
    can_write = db.Column(db.<PERSON>, default=False)
    can_delete = db.Column(db.<PERSON>, default=False)
    can_manage_users = db.Column(db.<PERSON>, default=False)
    can_view_reports = db.Column(db.Boolean, default=False)
    can_archive = db.Column(db.Boolean, default=False)
    can_approve = db.Column(db.Boolean, default=False)
    
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    users = db.relationship('User', backref='role', lazy='dynamic')
    
    def __repr__(self):
        return f'<Role {self.name_ar}>'

class Department(db.Model):
    """نموذج الإدارات والأقسام"""
    __tablename__ = 'departments'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    name_ar = db.Column(db.String(128), nullable=False)
    code = db.Column(db.String(10), unique=True, nullable=False)
    description = db.Column(db.Text)
    
    # الإدارة الرئيسية
    parent_id = db.Column(db.Integer, db.ForeignKey('departments.id'))
    parent = db.relationship('Department', remote_side=[id], backref='children')
    
    # معلومات الاتصال
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    location = db.Column(db.String(255))
    
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    # العلاقات
    users = db.relationship('User', backref='department', lazy='dynamic')
    
    def __repr__(self):
        return f'<Department {self.name_ar}>'

class User(UserMixin, db.Model):
    """نموذج المستخدمين"""
    __tablename__ = 'users'
    
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    
    # المعلومات الشخصية
    first_name = db.Column(db.String(64), nullable=False)
    last_name = db.Column(db.String(64), nullable=False)
    full_name_ar = db.Column(db.String(128), nullable=False)
    employee_id = db.Column(db.String(20), unique=True)
    
    # معلومات الاتصال
    phone = db.Column(db.String(20))
    mobile = db.Column(db.String(20))
    
    # الصورة الشخصية
    avatar = db.Column(db.String(255), default='default.png')
    
    # معلومات الحساب
    is_active = db.Column(db.Boolean, default=True)
    is_verified = db.Column(db.Boolean, default=False)
    last_login = db.Column(db.DateTime)
    login_count = db.Column(db.Integer, default=0)
    
    # التواريخ
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    updated_at = db.Column(db.DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # المفاتيح الخارجية
    role_id = db.Column(db.Integer, db.ForeignKey('roles.id'), nullable=False)
    department_id = db.Column(db.Integer, db.ForeignKey('departments.id'), nullable=False)
    
    def set_password(self, password):
        """تشفير كلمة المرور"""
        self.password_hash = generate_password_hash(password)
    
    def check_password(self, password):
        """التحقق من كلمة المرور"""
        return check_password_hash(self.password_hash, password)
    
    def update_login_info(self):
        """تحديث معلومات آخر دخول"""
        self.last_login = datetime.utcnow()
        self.login_count += 1
        db.session.commit()
    
    @property
    def full_name(self):
        """الاسم الكامل"""
        return f"{self.first_name} {self.last_name}"
    
    def has_permission(self, permission):
        """التحقق من الصلاحيات"""
        if not self.role:
            return False
        return getattr(self.role, permission, False)
    
    def __repr__(self):
        return f'<User {self.username}>'
