# -*- coding: utf-8 -*-
"""
نماذج قاعدة البيانات لنظام المراسلات الإلكترونية
Database Models for Electronic Correspondence System
"""

from flask_sqlalchemy import SQLAlchemy
from flask_login import UserMixin
from datetime import datetime
from werkzeug.security import generate_password_hash, check_password_hash

db = SQLAlchemy()

# استيراد جميع النماذج
from .user import User, Role, Department
from .correspondence import Correspondence, CorrespondenceType, Attachment, CorrespondenceAction
