# -*- coding: utf-8 -*-
"""
نظام المراسلات الإلكترونية - النسخة المحسنة
Electronic Correspondence System - Enhanced Version
"""

import os
import socket
from datetime import datetime
from flask import Flask, render_template, request, redirect, url_for, flash, session
from flask_sqlalchemy import SQLAlchemy
from flask_login import LoginManager, UserMixin, login_user, logout_user, login_required, current_user
from werkzeug.security import generate_password_hash, check_password_hash

# إنشاء التطبيق
app = Flask(__name__)
app.config['SECRET_KEY'] = 'your-secret-key-here'
app.config['SQLALCHEMY_DATABASE_URI'] = 'sqlite:///correspondence_enhanced.db'
app.config['SQLALCHEMY_TRACK_MODIFICATIONS'] = False

# إنشاء قاعدة البيانات
db = SQLAlchemy(app)

# إعداد نظام تسجيل الدخول
login_manager = LoginManager()
login_manager.init_app(app)
login_manager.login_view = 'login'
login_manager.login_message = 'يرجى تسجيل الدخول للوصول إلى هذه الصفحة.'

# نماذج قاعدة البيانات المحسنة
class Role(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), unique=True, nullable=False)
    name_ar = db.Column(db.String(64), nullable=False)
    description = db.Column(db.Text)

    # الصلاحيات
    can_read_all = db.Column(db.Boolean, default=False)
    can_write = db.Column(db.Boolean, default=False)
    can_delete = db.Column(db.Boolean, default=False)
    can_manage_users = db.Column(db.Boolean, default=False)
    can_view_reports = db.Column(db.Boolean, default=False)
    can_archive = db.Column(db.Boolean, default=False)

    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    users = db.relationship('User', backref='role', lazy='dynamic')

class Department(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(128), nullable=False)
    name_ar = db.Column(db.String(128), nullable=False)
    code = db.Column(db.String(10), unique=True, nullable=False)
    description = db.Column(db.Text)
    phone = db.Column(db.String(20))
    email = db.Column(db.String(120))
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    users = db.relationship('User', backref='department', lazy='dynamic')

class User(UserMixin, db.Model):
    id = db.Column(db.Integer, primary_key=True)
    username = db.Column(db.String(80), unique=True, nullable=False)
    email = db.Column(db.String(120), unique=True, nullable=False)
    password_hash = db.Column(db.String(255), nullable=False)
    full_name_ar = db.Column(db.String(128), nullable=False)
    employee_id = db.Column(db.String(20), unique=True)
    phone = db.Column(db.String(20))
    mobile = db.Column(db.String(20))
    is_active = db.Column(db.Boolean, default=True)
    is_admin = db.Column(db.Boolean, default=False)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    last_login = db.Column(db.DateTime)
    login_count = db.Column(db.Integer, default=0)

    role_id = db.Column(db.Integer, db.ForeignKey('role.id'))
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'))

    def set_password(self, password):
        self.password_hash = generate_password_hash(password)

    def check_password(self, password):
        return check_password_hash(self.password_hash, password)

    def update_login_info(self):
        self.last_login = datetime.utcnow()
        self.login_count += 1
        db.session.commit()

    def has_permission(self, permission):
        if not self.role:
            return self.is_admin
        return getattr(self.role, permission, False) or self.is_admin

class CorrespondenceType(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(64), nullable=False)
    name_ar = db.Column(db.String(64), nullable=False)
    code = db.Column(db.String(10), unique=True, nullable=False)
    description = db.Column(db.Text)
    color = db.Column(db.String(7), default='#007bff')
    is_active = db.Column(db.Boolean, default=True)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    correspondences = db.relationship('Correspondence', backref='type', lazy='dynamic')

class Correspondence(db.Model):
    id = db.Column(db.Integer, primary_key=True)
    internal_number = db.Column(db.String(20), unique=True, nullable=False)
    external_number = db.Column(db.String(50))
    subject = db.Column(db.String(255), nullable=False)
    content = db.Column(db.Text)
    sender_name = db.Column(db.String(255))
    sender_organization = db.Column(db.String(255))
    recipient_name = db.Column(db.String(255))
    recipient_organization = db.Column(db.String(255))
    direction = db.Column(db.String(10), nullable=False)
    status = db.Column(db.String(20), default='جديد')
    priority = db.Column(db.String(20), default='عادي')
    classification = db.Column(db.String(50))
    action_required = db.Column(db.String(255))
    action_taken = db.Column(db.Text)
    notes = db.Column(db.Text)

    date_created = db.Column(db.DateTime, default=datetime.utcnow)
    date_received = db.Column(db.DateTime)
    date_sent = db.Column(db.DateTime)
    due_date = db.Column(db.DateTime)

    is_archived = db.Column(db.Boolean, default=False)
    is_confidential = db.Column(db.Boolean, default=False)

    created_by_id = db.Column(db.Integer, db.ForeignKey('user.id'), nullable=False)
    assigned_to_id = db.Column(db.Integer, db.ForeignKey('user.id'))
    department_id = db.Column(db.Integer, db.ForeignKey('department.id'))
    type_id = db.Column(db.Integer, db.ForeignKey('correspondence_type.id'))

    created_by = db.relationship('User', foreign_keys=[created_by_id], backref='created_correspondences')
    assigned_to = db.relationship('User', foreign_keys=[assigned_to_id], backref='assigned_correspondences')

    def generate_internal_number(self):
        year = datetime.now().year
        count = Correspondence.query.filter(
            db.extract('year', Correspondence.date_created) == year
        ).count() + 1

        if self.direction == 'صادر':
            prefix = 'OUT'
        else:
            prefix = 'IN'

        self.internal_number = f"{prefix}-{year}-{count:04d}"

    @property
    def days_since_received(self):
        if self.date_received:
            return (datetime.utcnow() - self.date_received).days
        return 0

    @property
    def is_overdue(self):
        if self.due_date:
            return datetime.utcnow() > self.due_date
        return False

@login_manager.user_loader
def load_user(user_id):
    return User.query.get(int(user_id))

def get_local_ip():
    try:
        s = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
        s.connect(("*******", 80))
        ip = s.getsockname()[0]
        s.close()
        return ip
    except:
        return '127.0.0.1'

# المسارات
@app.route('/')
def index():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))
    return redirect(url_for('login'))

@app.route('/login', methods=['GET', 'POST'])
def login():
    if current_user.is_authenticated:
        return redirect(url_for('dashboard'))

    if request.method == 'POST':
        username = request.form.get('username', '').strip()
        password = request.form.get('password', '')

        if not username or not password:
            flash('يرجى إدخال اسم المستخدم وكلمة المرور', 'error')
            return render_template('simple_login.html')

        user = User.query.filter(
            (User.username == username) | (User.email == username)
        ).first()

        if user and user.check_password(password):
            if not user.is_active:
                flash('حسابك غير مفعل. يرجى التواصل مع مدير النظام', 'error')
                return render_template('simple_login.html')

            login_user(user, remember=bool(request.form.get('remember_me')))
            user.update_login_info()

            flash(f'مرحباً بك {user.full_name_ar}', 'success')
            return redirect(url_for('dashboard'))
        else:
            flash('اسم المستخدم أو كلمة المرور غير صحيحة', 'error')

    return render_template('simple_login.html')

@app.route('/logout')
@login_required
def logout():
    user_name = current_user.full_name_ar
    logout_user()
    flash(f'تم تسجيل خروجك بنجاح. وداعاً {user_name}', 'info')
    return redirect(url_for('login'))

@app.route('/dashboard')
@login_required
def dashboard():
    # إحصائيات محسنة
    total_correspondences = Correspondence.query.count()
    incoming_count = Correspondence.query.filter_by(direction='وارد').count()
    outgoing_count = Correspondence.query.filter_by(direction='صادر').count()

    # المراسلات الحديثة مع الصلاحيات
    if current_user.has_permission('can_read_all'):
        recent_correspondences = Correspondence.query.order_by(
            Correspondence.date_created.desc()
        ).limit(10).all()
    else:
        recent_correspondences = Correspondence.query.filter(
            (Correspondence.created_by_id == current_user.id) |
            (Correspondence.assigned_to_id == current_user.id) |
            (Correspondence.department_id == current_user.department_id)
        ).order_by(Correspondence.date_created.desc()).limit(10).all()

    stats = {
        'total_correspondences': total_correspondences,
        'incoming_count': incoming_count,
        'outgoing_count': outgoing_count,
        'pending_count': Correspondence.query.filter_by(status='جديد').count(),
        'archived_count': Correspondence.query.filter_by(is_archived=True).count(),
        'overdue_count': Correspondence.query.filter(
            Correspondence.due_date < datetime.utcnow(),
            Correspondence.status != 'مكتمل'
        ).count()
    }

    return render_template('enhanced_dashboard.html',
                         stats=stats,
                         recent_correspondences=recent_correspondences)

# المسارات الأساسية المطلوبة
@app.route('/correspondence/new', methods=['GET', 'POST'])
@login_required
def new_correspondence():
    if request.method == 'POST':
        direction = request.form.get('direction')
        subject = request.form.get('subject', '').strip()
        content = request.form.get('content', '').strip()
        priority = request.form.get('priority', 'عادي')

        if not subject:
            flash('يرجى إدخال موضوع المراسلة', 'error')
            return render_template('simple_new_correspondence.html')

        correspondence = Correspondence(
            subject=subject,
            content=content,
            direction=direction,
            priority=priority,
            created_by_id=current_user.id
        )

        if direction == 'وارد':
            correspondence.sender_name = request.form.get('sender_name', '')
            correspondence.sender_organization = request.form.get('sender_organization', '')
            correspondence.external_number = request.form.get('external_number', '')
            correspondence.date_received = datetime.utcnow()
        else:
            correspondence.recipient_name = request.form.get('recipient_name', '')
            correspondence.recipient_organization = request.form.get('recipient_organization', '')
            correspondence.date_sent = datetime.utcnow()

        correspondence.generate_internal_number()

        try:
            db.session.add(correspondence)
            db.session.commit()
            flash(f'تم إضافة المراسلة بنجاح برقم {correspondence.internal_number}', 'success')

            if direction == 'وارد':
                return redirect(url_for('inbox'))
            else:
                return redirect(url_for('outbox'))
        except Exception as e:
            db.session.rollback()
            flash('حدث خطأ أثناء حفظ المراسلة', 'error')

    return render_template('simple_new_correspondence.html')

@app.route('/inbox')
@login_required
def inbox():
    query = Correspondence.query.filter_by(direction='وارد')

    if not current_user.has_permission('can_read_all'):
        query = query.filter(
            (Correspondence.assigned_to_id == current_user.id) |
            (Correspondence.department_id == current_user.department_id)
        )

    correspondences = query.order_by(Correspondence.date_created.desc()).all()
    return render_template('simple_inbox.html', correspondences=correspondences)

@app.route('/outbox')
@login_required
def outbox():
    query = Correspondence.query.filter_by(direction='صادر')

    if not current_user.has_permission('can_read_all'):
        query = query.filter(
            (Correspondence.created_by_id == current_user.id) |
            (Correspondence.department_id == current_user.department_id)
        )

    correspondences = query.order_by(Correspondence.date_created.desc()).all()
    return render_template('simple_outbox.html', correspondences=correspondences)

@app.route('/correspondence/<int:id>')
@login_required
def view_correspondence(id):
    correspondence = Correspondence.query.get_or_404(id)
    return render_template('simple_view_correspondence.html', correspondence=correspondence)

# متغيرات القوالب العامة
@app.context_processor
def inject_globals():
    return {
        'system_name': 'نظام المراسلات الإلكترونية المحسن',
        'organization_name': 'الجهة الحكومية',
        'system_version': '1.1.0',
        'current_ip': get_local_ip()
    }

def create_initial_data():
    """إنشاء البيانات الأولية المحسنة"""

    # إنشاء الأدوار
    if not Role.query.first():
        roles = [
            {
                'name': 'admin', 'name_ar': 'مدير النظام',
                'can_read_all': True, 'can_write': True, 'can_delete': True,
                'can_manage_users': True, 'can_view_reports': True, 'can_archive': True
            },
            {
                'name': 'manager', 'name_ar': 'مدير إدارة',
                'can_read_all': True, 'can_write': True, 'can_view_reports': True, 'can_archive': True
            },
            {
                'name': 'employee', 'name_ar': 'موظف',
                'can_write': True
            }
        ]

        for role_data in roles:
            role = Role(**role_data)
            db.session.add(role)

    # إنشاء الإدارات
    if not Department.query.first():
        departments = [
            {'name': 'Administration', 'name_ar': 'الإدارة العامة', 'code': 'ADM'},
            {'name': 'IT', 'name_ar': 'تقنية المعلومات', 'code': 'IT'},
            {'name': 'HR', 'name_ar': 'الموارد البشرية', 'code': 'HR'}
        ]

        for dept_data in departments:
            dept = Department(**dept_data)
            db.session.add(dept)

    # إنشاء أنواع المراسلات
    if not CorrespondenceType.query.first():
        types = [
            {'name': 'Official', 'name_ar': 'رسمي', 'code': 'OFF', 'color': '#007bff'},
            {'name': 'Internal', 'name_ar': 'داخلي', 'code': 'INT', 'color': '#28a745'},
            {'name': 'External', 'name_ar': 'خارجي', 'code': 'EXT', 'color': '#ffc107'}
        ]

        for type_data in types:
            corr_type = CorrespondenceType(**type_data)
            db.session.add(corr_type)

    # إنشاء المستخدم الافتراضي
    if not User.query.first():
        admin_role = Role.query.filter_by(name='admin').first()
        admin_dept = Department.query.filter_by(code='ADM').first()

        admin_user = User(
            username='admin',
            email='<EMAIL>',
            full_name_ar='مدير النظام',
            employee_id='ADM001',
            is_active=True,
            is_admin=True,
            role_id=admin_role.id if admin_role else None,
            department_id=admin_dept.id if admin_dept else None
        )
        admin_user.set_password('admin123')
        db.session.add(admin_user)

    db.session.commit()

if __name__ == '__main__':
    with app.app_context():
        db.create_all()
        create_initial_data()

    local_ip = get_local_ip()
    port = 5001  # منفذ مختلف لتجنب التعارض

    print("=" * 60)
    print("🚀 نظام المراسلات الإلكترونية المحسن")
    print("=" * 60)
    print("🌐 عناوين الوصول:")
    print(f"   المحلي: http://127.0.0.1:{port}")
    print(f"   الشبكة: http://{local_ip}:{port}")
    print("=" * 60)
    print("👤 بيانات الدخول:")
    print("   اسم المستخدم: admin")
    print("   كلمة المرور: admin123")
    print("=" * 60)

    app.run(host='0.0.0.0', port=port, debug=True)
